import type * as tables from './schema';

export type UsersRequest = typeof tables.usersTable.$inferInsert;
export type UsersResponse = typeof tables.usersTable.$inferSelect;

export type BoardsRequest = typeof tables.boardsTable.$inferInsert;
export type BoardsResponse = Omit<
  typeof tables.boardsTable.$inferSelect,
  'userId'
> & {
  user: Pick<UsersResponse, 'id' | 'name' | 'avatar' | 'email'>;
};
