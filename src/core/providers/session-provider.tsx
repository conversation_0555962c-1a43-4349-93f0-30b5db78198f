'use client';

import type { Maybe, Session } from '@/types';
import type { Dispatch, PropsWithChildren, SetStateAction } from 'react';

import { createContext, useState } from 'react';

export interface SessionContextValue {
  session: Maybe<Session>;
  setSession: Dispatch<SetStateAction<Maybe<Session>>>;
}

export const SessionContext = createContext<SessionContextValue>({
  session: undefined,
  setSession: () => undefined,
});

export function SessionProvider({
  defaultSession,
  children,
}: PropsWithChildren<{
  defaultSession?: Maybe<Session>;
}>) {
  const [session, setSession] = useState<Maybe<Session>>(defaultSession);
  return (
    <SessionContext.Provider value={{ session, setSession }}>
      {children}
    </SessionContext.Provider>
  );
}
