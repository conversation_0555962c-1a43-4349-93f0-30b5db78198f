import type { Supabase } from '@/lib/supabase/types';

import { eq } from 'drizzle-orm';
import { redirect } from 'next/navigation';
import { cache } from 'react';

import { dbAdmin } from '@/database';
import { usersTable } from '@/database/schema';

import { routes } from '@/configuration';
import { getRedirectToSignIn } from '@/lib/supabase/auth/redirect';
import { getSupabaseServerClient } from '@/lib/supabase/config/server';

const dedupedGetUserInfo = cache(async (supabase: Supabase, userId: string) => {
  const [userInfo] = await dbAdmin
    .select({
      name: usersTable.name,
      avatar: usersTable.avatar,
    })
    .from(usersTable)
    .where(eq(usersTable.id, userId))
    .limit(1);

  if (!userInfo) {
    await supabase.auth.signOut();
    return redirect(routes.auth.SignIn);
  }

  return userInfo;
});

export async function getAuthContext() {
  const supabase = await getSupabaseServerClient();
  const { data: auth } = await supabase.auth.getUser();

  if (!auth.user || !auth.user.id || !auth.user.email) {
    return redirect(getRedirectToSignIn());
  }

  const userInfo = await dedupedGetUserInfo(supabase, auth.user.id);

  const enrichedSession = {
    user: {
      id: auth.user.id,
      email: auth.user.email,
      ...userInfo,
    },
  };

  return { supabase, session: enrichedSession };
}
