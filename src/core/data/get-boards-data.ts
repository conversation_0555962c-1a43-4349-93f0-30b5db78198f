import 'server-only';

import type { BoardsResponse } from '@/database/types';

import { eq } from 'drizzle-orm';
import { unstable_cache as cache } from 'next/cache';

import { dbAdmin } from '@/database';
import { boardsTable } from '@/database/schema';

import { getAuthContext } from '@/core/data/context';
import {
  CacheKey,
  Caching,
  defaultRevalidateTimeInSeconds,
} from '@/lib/caching';

export async function getBoardsData(): Promise<BoardsResponse[]> {
  const ctx = await getAuthContext();

  return cache(
    async () => {
      const boards = await dbAdmin
        .select({
          id: boardsTable.id,
          name: boardsTable.name,
          createdAt: boardsTable.createdAt,
          updatedAt: boardsTable.updatedAt,
        })
        .from(boardsTable)
        .where(eq(boardsTable.userId, ctx.session.user.id));

      if (!boards) {
        throw new Error('Boards not found');
      }

      const response: BoardsResponse[] = boards.map((board) => ({
        ...board,
        user: ctx.session.user,
      }));
      return response;
    },
    Caching.createKeyParts(CacheKey.BoardsData, ctx.session.user.id),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [Caching.createTag(CacheKey.BoardsData, ctx.session.user.id)],
    }
  )();
}
