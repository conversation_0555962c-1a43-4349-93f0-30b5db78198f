'use server';

import { dbAdmin } from '@/database';
import { boardsTable } from '@/database/schema';
import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { authActionClient } from '@/actions/safe-action';
import { CacheKey, Caching } from '@/lib/caching';
import { logger } from '@/utils/logger';
import { eq } from 'drizzle-orm';

const createBoardSchema = z.object({
  boardName: z.string().min(3, 'Board name is required'),
});

export const createBoardAction = authActionClient
  .schema(createBoardSchema)
  .metadata({ name: 'create-board' })
  .action(async ({ ctx: { session }, parsedInput: { boardName } }) => {
    const userId = session.user.id;

    const [newBoard] = await dbAdmin
      .insert(boardsTable)
      .values({ name: boardName, userId })
      .returning({ boardId: boardsTable.id });

    const { boardId } = newBoard ?? {};

    if (!boardId) {
      throw new Error('❌ ERROR IN CREATING BOARD');
    }

    logger.info({ userId, boardId }, '✅ BOARD CREATED SUCCESSFULLY');

    revalidateTag(Caching.createTag(CacheKey.BoardsData, userId));

    return boardId;
  });

const renameBoardSchema = z.object({
  boardId: z.string().uuid().min(3, 'Board name is required'),
  boardName: z.string().min(3, 'Board name is required'),
});

export const renameBoardAction = authActionClient
  .schema(renameBoardSchema)
  .metadata({ name: 'rename-board' })
  .action(async ({ ctx: { session }, parsedInput: { boardId, boardName } }) => {
    const userId = session.user.id;

    await dbAdmin
      .update(boardsTable)
      .set({ name: boardName })
      .where(eq(boardsTable.id, boardId));

    logger.info({ userId, boardId }, '✅ BOARD RENAMED SUCCESSFULLY');

    revalidateTag(Caching.createTag(CacheKey.BoardsData, userId));

    return boardId;
  });

const toggleStarBoardSchema = z.object({
  boardId: z.string().uuid().min(3, 'Board name is required'),
  isStarred: z.boolean(),
});

export const starBoardAction = authActionClient
  .schema(toggleStarBoardSchema)
  .metadata({ name: 'toggle-star-board' })
  .action(async ({ ctx: { session }, parsedInput: { boardId, isStarred } }) => {
    const userId = session.user.id;

    await dbAdmin
      .update(boardsTable)
      .set({ starred: isStarred })
      .where(eq(boardsTable.id, boardId));

    revalidateTag(Caching.createTag(CacheKey.BoardsData, userId));

    logger.info({ userId, boardId }, '✅ BOARD TOGGLED STARRED SUCCESSFULLY');
  });

const deleteBoardSchema = z.object({
  boardId: z.string().uuid().min(3, 'Board name is required'),
});

export const deleteBoardAction = authActionClient
  .schema(deleteBoardSchema)
  .metadata({ name: 'delete-board' })
  .action(async ({ ctx: { session }, parsedInput: { boardId } }) => {
    const userId = session.user.id;

    await dbAdmin.delete(boardsTable).where(eq(boardsTable.id, boardId));

    revalidateTag(Caching.createTag(CacheKey.BoardsData, userId));

    logger.info({ userId, boardId }, '✅ BOARD DELETED SUCCESSFULLY');
  });
