'use client';

import NiceModal from '@ebay/nice-modal-react';
import { motion } from 'framer-motion';
import {
  ArrowRightIcon,
  FolderIcon,
  ImageIcon,
  LayoutGridIcon,
  MessageSquareIcon,
  PlusIcon,
  SparklesIcon,
} from 'lucide-react';
import { useCallback } from 'react';

import { NewBoardModal } from '@/components/dashboard/new-board-modal';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export function EmptyBoardsState() {
  const handleNewBoard = useCallback(() => {
    NiceModal.show(NewBoardModal);
  }, []);

  const features = [
    {
      icon: MessageSquareIcon,
      title: 'AI-Powered Conversations',
      description: 'Chat with AI models and get intelligent responses',
    },
    {
      icon: ImageIcon,
      title: 'Visual Content',
      description: 'Upload images, videos, and documents',
    },
    {
      icon: FolderIcon,
      title: 'Organized Workspace',
      description: 'Keep your projects structured and accessible',
    },
  ];

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-8 max-w-2xl mx-auto"
      >
        {/* Main illustration */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="relative"
        >
          <div className="relative mx-auto w-32 h-32 mb-6">
            <motion.div
              animate={{
                rotate: [0, 5, -5, 0],
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 4,
                repeat: Number.POSITIVE_INFINITY,
                ease: 'easeInOut',
              }}
              className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5 rounded-2xl border border-primary/20"
            />
            <motion.div
              animate={{
                y: [0, -4, 0],
              }}
              transition={{
                duration: 3,
                repeat: Number.POSITIVE_INFINITY,
                ease: 'easeInOut',
              }}
              className="absolute inset-4 bg-background/95 backdrop-blur-sm border rounded-xl flex items-center justify-center shadow-lg"
            >
              <LayoutGridIcon className="h-12 w-12 text-primary" />
            </motion.div>

            {/* Floating elements */}
            <motion.div
              animate={{
                y: [0, -8, 0],
                x: [0, 4, 0],
              }}
              transition={{
                duration: 2.5,
                repeat: Number.POSITIVE_INFINITY,
                ease: 'easeInOut',
                delay: 0.5,
              }}
              className="absolute -top-2 -right-2 w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center"
            >
              <SparklesIcon className="h-3 w-3 text-primary" />
            </motion.div>

            <motion.div
              animate={{
                y: [0, -6, 0],
                x: [0, -3, 0],
              }}
              transition={{
                duration: 3.5,
                repeat: Number.POSITIVE_INFINITY,
                ease: 'easeInOut',
                delay: 1,
              }}
              className="absolute -bottom-1 -left-1 w-4 h-4 bg-accent rounded-full"
            />
          </div>
        </motion.div>

        {/* Main content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-4"
        >
          <h1 className="text-3xl font-semibold text-foreground">
            Create Your First Board
          </h1>
          <p className="text-lg text-muted-foreground max-w-md mx-auto">
            Start building amazing AI-powered content with our interactive
            canvas. Organize your ideas, chat with AI, and bring your projects
            to life.
          </p>
        </motion.div>

        {/* Action buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="flex flex-col sm:flex-row gap-3 justify-center"
        >
          <Button
            onClick={handleNewBoard}
            size="lg"
            className="group relative overflow-hidden"
          >
            <motion.div
              className="flex items-center gap-2"
              whileHover={{ x: 2 }}
              transition={{ duration: 0.2 }}
            >
              <PlusIcon className="h-4 w-4" />
              Create New Board
              <ArrowRightIcon className="h-4 w-4 transition-transform group-hover:translate-x-1" />
            </motion.div>
          </Button>

          <Button variant="outline" size="lg" className="group">
            <SparklesIcon className="h-4 w-4 mr-2 transition-transform group-hover:rotate-12" />
            Explore Templates
          </Button>
        </motion.div>

        {/* Feature cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-12"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
              whileHover={{ y: -4 }}
            >
              <Card className="border-border/50 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300">
                <CardContent className="p-6 text-center space-y-3">
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-medium text-foreground">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
}
