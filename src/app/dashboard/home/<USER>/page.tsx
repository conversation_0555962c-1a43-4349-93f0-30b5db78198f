import { BoardCard } from '@/components/dashboard/board-card';
import { NewBoardModal } from '@/components/dashboard/new-board-modal';
import { Button } from '@/components/ui/button';
import { getBoardsData } from '@/core/data/get-boards-data';
import { PlusIcon } from 'lucide-react';

export default async function BoardsList() {
  const boards = await getBoardsData();

  return (
    <div className="px-4 pb-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <h1 className="text-2xl font-semibold mb-4 sm:mb-0">Dashboard</h1>
        <div className="flex flex-wrap items-center gap-2">
          <NewBoardModal>
            <Button className="border border-white" variant="outline">
              <PlusIcon />
              <span>New board</span>
            </Button>
          </NewBoardModal>
          <Button variant="default">New collection</Button>
        </div>
      </div>

      {/* <div className="flex flex-col sm:flex-row gap-3 mb-6">
        <div className="flex-1 flex flex-col sm:flex-row gap-3">
          <Input
            placeholder="Search Boards..."
            leadingIcon={<SearchIcon className="size-4" />}
            className="md:max-w-md"
          />

          <Button variant="default">Search</Button>
        </div>

        <div className="flex gap-3">
          <Select defaultValue="lastUpdated">
            <SelectTrigger className="w-full sm:w-32">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="lastUpdated">Last updated</SelectItem>
              <SelectItem value="dateCreated">Date created</SelectItem>
              <SelectItem value="name">Name</SelectItem>
            </SelectContent>
          </Select>

          <Select defaultValue="descending">
            <SelectTrigger className="w-full sm:w-32">
              <SelectValue placeholder="Order" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ascending">Ascending</SelectItem>
              <SelectItem value="descending">Descending</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div> */}

      <h2 className="text-xl font-semibold mb-6">Boards</h2>

      <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6 gap-8 mb-12">
        {boards.map((board) => (
          <BoardCard key={board.id} board={board} />
        ))}
      </div>

      {/* <h2 className="text-lg font-medium mb-4 dark:text-white">
        Recent Activities
      </h2> */}
    </div>
  );
}
