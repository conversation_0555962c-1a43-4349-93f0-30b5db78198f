import { DashboardHeader } from '@/components/dashboard/dashboard-header';

interface DashboardHomeLayoutProps {
  boardsList: React.ReactNode;
}

export default function DashboardHomeLayout({
  boardsList,
}: DashboardHomeLayoutProps) {
  return (
    <div className="min-h-screen">
      <DashboardHeader
        breadcrumbs={[{ label: 'Dashboard', isCurrentPage: true }]}
      />

      {boardsList}
    </div>
  );
}
