'use client';

import NiceModal from '@ebay/nice-modal-react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState } from 'react';

export function RootProviders({ children }: React.PropsWithChildren) {
  const [client] = useState(new QueryClient());

  return (
    <QueryClientProvider client={client}>
      <NiceModal.Provider>{children}</NiceModal.Provider>
    </QueryClientProvider>
  );
}
