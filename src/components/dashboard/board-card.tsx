'use client';

import type { BoardsResponse } from '@/database/types';

import { format, formatDistanceToNow, isAfter, subMonths } from 'date-fns';
import { MoreVerticalIcon, StarIcon } from 'lucide-react';
import { useState } from 'react';

import { GradientHeader } from '@/components/dashboard/gradient-header';
import { Button } from '@/components/ui/button';
import { cn } from '@/utils/cn';
import { BoardOptions } from './board-options';

interface BoardCardProps {
  board: BoardsResponse;
}

export function BoardCard({ board }: BoardCardProps) {
  const [isStarred, setIsStarred] = useState(board.starred);

  const toggleStar = () => {
    setIsStarred(!isStarred);
    // Here you would typically update the board in the database
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const oneMonthAgo = subMonths(now, 1);

    if (isAfter(date, oneMonthAgo)) {
      return `Last Modified ${formatDistanceToNow(date, { addSuffix: true })}`;
    }

    return `Last Modified: ${format(date, 'MMM d, yyyy')}`;
  };

  const formattedDate = formatDate(board.updatedAt);

  return (
    <div
      className="group relative flex h-full flex-col overflow-hidden rounded-xl border bg-card cursor-pointer transition-all duration-300 hover:bg-accent hover:border-accent p-3"
      onClick={() => console.log(`Opening board: ${board.id}`)}
    >
      <div className="relative mb-3">
        <GradientHeader className="h-44 overflow-hidden rounded-lg" />
        <Button
          size="icon"
          variant="ghost"
          className={cn(
            'absolute left-2 top-2 size-7 text-yellow-500 hover:text-yellow-500 border-0 hover:[&>svg]:fill-yellow-500 !bg-accent transition-all',
            !isStarred && 'opacity-0 group-hover:opacity-100'
          )}
          onClick={toggleStar}
        >
          <StarIcon
            className={cn(
              'size-4',
              isStarred ? 'fill-yellow-500' : 'fill-none'
            )}
          />
        </Button>
      </div>

      <div className="flex items-start justify-between gap-2 mb-1">
        <div className="flex flex-col justify-center gap-1.5 min-w-0">
          <h3 className="line-clamp-2 text-base font-medium">{board.name}</h3>
          <p className="text-xs text-muted-foreground pb-2">{formattedDate}</p>
        </div>

        <BoardOptions
          boardId={board.id}
          isStarred={isStarred}
          onStarToggle={toggleStar}
        >
          <Button
            size="sm"
            variant="ghost"
            className="size-7 hover:!bg-background"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreVerticalIcon className="h-4 w-4" />
          </Button>
        </BoardOptions>
      </div>
    </div>
  );
}
