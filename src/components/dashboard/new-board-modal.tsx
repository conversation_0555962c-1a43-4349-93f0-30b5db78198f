'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { createBoardAction } from '@/actions/board';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/form-input';
import { routes } from '@/configuration';

const newBoardSchema = z.object({
  boardName: z.string().min(3, 'Board name is required'),
});

type NewBoardFormValues = z.infer<typeof newBoardSchema>;

export const NewBoardModal = ({ children }: React.PropsWithChildren) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);

  const form = useForm<NewBoardFormValues>({
    resolver: zodResolver(newBoardSchema),
    defaultValues: {
      boardName: '',
    },
  });

  const isSubmitting = form.formState.isSubmitting;
  const canSubmit =
    !form.formState.isSubmitting &&
    (!form.formState.isSubmitted || form.formState.isDirty);

  const onSubmit = async ({ boardName }: NewBoardFormValues) => {
    if (!canSubmit) {
      return;
    }
    const promise = createBoardAction({ boardName });

    toast.promise(promise, {
      loading: 'Creating board...',
      success: (response) => {
        const boardId = response?.data;
        if (boardId) {
          router.push(`${routes.dashboard.Index}/${boardId}`);
        }
        return 'Board created successfully';
      },
      error: (error) => {
        console.error({ error });
        return 'Failed to create board';
      },
      finally: () => {
        form.reset();
        setIsOpen(false);
      },
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Board</DialogTitle>
          <DialogDescription>
            Enter a name for your new board.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormInput
              form={form}
              fieldName="boardName"
              fieldLabel="Board Name"
              placeholder="My Awesome Board"
              disabled={isSubmitting}
              required
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                Create Board
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
