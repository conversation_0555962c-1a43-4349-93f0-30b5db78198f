import {
  CopyIcon,
  ExternalLinkIcon,
  PencilIcon,
  StarIcon,
  TrashIcon,
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/utils/cn';

interface BoardOptionsProps extends React.PropsWithChildren {
  boardId: string;
  isStarred: boolean;
  onStarToggle: VoidFunction;
}

export function BoardOptions({
  boardId,
  isStarred,
  onStarToggle,
  children,
}: BoardOptionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuItem>
          <PencilIcon className="mr-2 h-4 w-4" />
          <span>Rename</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <ExternalLinkIcon className="mr-2 h-4 w-4" />
          <span>Open in new tab</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onStarToggle}>
          <StarIcon
            className={cn(
              'mr-2 size-4',
              isStarred && 'fill-yellow-500 stroke-none'
            )}
          />
          <span>{isStarred ? 'Unstar board' : 'Star board'}</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <CopyIcon className="mr-2 h-4 w-4" />
          <span>Duplicate</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem variant="destructive">
          <TrashIcon className="mr-2 h-4 w-4" />
          <span>Delete</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
