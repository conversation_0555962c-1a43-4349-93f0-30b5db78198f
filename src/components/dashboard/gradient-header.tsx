'use client';

import { cn } from '@/utils/cn';
import { useEffect, useState } from 'react';

interface GradientHeaderProps {
  className?: string;
}

export function GradientHeader({ className }: GradientHeaderProps) {
  const [gradient, setGradient] = useState('');

  useEffect(() => {
    const baseHue = Math.floor(Math.random() * 360);
    const secondHue = (baseHue + 30) % 360;
    setGradient(
      `linear-gradient(135deg, hsl(${baseHue}, 65%, 75%) 0%, hsl(${secondHue}, 70%, 65%) 100%)`
    );
  }, []);

  return (
    <div
      className={cn('relative w-full rounded-t-xl', className)}
      style={{ background: gradient }}
    >
      <div
        className="absolute inset-0 rounded-t-xl opacity-30"
        style={{
          backgroundImage: `linear-gradient(to right, rgba(255, 255, 255, 0.3) 1px, transparent 1px), 
                            linear-gradient(to bottom, rgba(255, 255, 255, 0.3) 1px, transparent 1px)`,
          backgroundSize: '16px 16px',
        }}
      />

      <div className="absolute inset-0 overflow-hidden p-3">
        <div className="absolute left-6 top-4 h-8 w-12 rounded bg-white/20" />
        <div className="absolute right-10 top-6 h-8 w-8 rounded-full bg-white/20" />
        <div className="absolute left-12 bottom-6 h-6 w-16 rounded bg-white/20" />
      </div>
    </div>
  );
}
