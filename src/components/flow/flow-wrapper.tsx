'use client';

import { useReactFlow } from '@xyflow/react';
import { useCallback, useRef } from 'react';

interface FlowWrapperProps extends React.PropsWithChildren {
  onAddNode: (type: string, position?: { x: number; y: number }) => void;
}

export function FlowWrapper({ onAddNode, children }: FlowWrapperProps) {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { screenToFlowPosition } = useReactFlow();

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      const nodeType = event.dataTransfer.getData('application/reactflow');
      if (nodeType && reactFlowWrapper.current) {
        const reactFlowBounds =
          reactFlowWrapper.current.getBoundingClientRect();
        const position = screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        });
        onAddNode(nodeType, position);
      }
    },
    [onAddNode, screenToFlowPosition]
  );

  return (
    <div
      className="w-full h-full relative"
      ref={reactFlowWrapper}
      onDrop={onDrop}
      onDragOver={onDragOver}
    >
      {children}
    </div>
  );
}
