import { cn } from '@/utils/cn';

interface TikTokIconProps {
  className?: string;
}

export const TikTokIcon: React.FC<TikTokIconProps> = ({ className }) => {
  return (
    <svg
      className={cn('h-6 w-6', className)}
      fill="currentColor"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path d="M19.321 5.562a5.124 5.124 0 0 1-.443-.258 6.228 6.228 0 0 1-1.137-.966c-.849-.849-1.204-1.864-1.204-2.338C16.537 1.434 16.07 1 15.5 1h-3.038c-.57 0-1.037.434-1.037 1v11.5c0 1.381-1.119 2.5-2.5 2.5s-2.5-1.119-2.5-2.5 1.119-2.5 2.5-2.5c.276 0 .5-.224.5-.5V7.462c0-.276-.224-.5-.5-.5-3.584 0-6.5 2.916-6.5 6.5s2.916 6.5 6.5 6.5 6.5-2.916 6.5-6.5V8.378c.713.459 1.556.734 2.462.734.276 0 .5-.224.5-.5V6.062c0-.276-.224-.5-.5-.5-.381 0-.741-.1-1.062-.278z"/>
    </svg>
  );
};
