import { NavigationGuardProvider } from 'next-navigation-guard';
import { ThemeProvider } from 'next-themes';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';

import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { cn } from '@/utils/cn';

import '@/app/globals.css';
import { RootProviders } from '@/components/root-providers';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(geistSans.variable, geistMono.variable, 'antialiased')}
        suppressHydrationWarning
      >
        <NavigationGuardProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            disableTransitionOnChange
          >
            <Toaster position="top-right" theme="light" richColors />
            <TooltipProvider>
              <RootProviders>{children}</RootProviders>
            </TooltipProvider>
          </ThemeProvider>
        </NavigationGuardProvider>
      </body>
    </html>
  );
}
